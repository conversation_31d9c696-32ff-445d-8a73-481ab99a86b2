"""
Qt修复工具 - 解决PyQt5常见警告和问题
"""

import sys
import os
import platform
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# 尝试导入可选的Qt功能
try:
    from PyQt5.QtCore import qRegisterMetaType
except ImportError:
    def qRegisterMetaType(type_name):
        pass

try:
    from PyQt5.QtCore import QLoggingCategory
except ImportError:
    QLoggingCategory = None

try:
    from PyQt5.QtCore import qInstallMessageHandler
except ImportError:
    qInstallMessageHandler = None


def register_qt_types():
    """注册Qt元类型以避免警告"""
    try:
        # 注册常见的Qt类型
        qRegisterMetaType("QVector<int>")
        qRegisterMetaType("QList<int>")
        qRegisterMetaType("QModelIndex")
        qRegisterMetaType("QItemSelection")
        qRegisterMetaType("QAbstractItemModel::LayoutChangeHint")
        print("✓ Qt元类型注册成功")
    except Exception as e:
        print(f"⚠️ Qt元类型注册失败: {e}")


def setup_qt_logging():
    """设置Qt日志过滤，减少不必要的警告"""
    if QLoggingCategory is None:
        print("⚠️ QLoggingCategory不可用，跳过日志过滤设置")
        return

    try:
        # 禁用某些Qt警告类别
        QLoggingCategory.setFilterRules("""
            qt.qpa.cocoa.drawing.debug=false
            qt.qpa.drawing.debug=false
            qt.widgets.painting.debug=false
            qt.core.qobject.connect.debug=false
        """)
        print("✓ Qt日志过滤设置成功")
    except Exception as e:
        print(f"⚠️ Qt日志过滤设置失败: {e}")


def setup_qt_attributes():
    """设置Qt应用程序属性以优化性能和减少警告"""
    try:
        # 高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 绘图优化
        QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)
        QApplication.setAttribute(Qt.AA_ShareOpenGLContexts, True)
        
        # 减少绘图警告
        QApplication.setAttribute(Qt.AA_CompressHighFrequencyEvents, True)
        QApplication.setAttribute(Qt.AA_CompressTabletEvents, True)
        
        print("✓ Qt属性设置成功")
    except Exception as e:
        print(f"⚠️ Qt属性设置失败: {e}")


def suppress_qt_warnings():
    """抑制Qt相关的警告输出"""
    import warnings
    
    # 过滤特定的Qt警告
    warnings.filterwarnings("ignore", category=DeprecationWarning, module=".*qt.*")
    warnings.filterwarnings("ignore", message=".*QVector.*")
    warnings.filterwarnings("ignore", message=".*QPainter.*")
    warnings.filterwarnings("ignore", message=".*QBackingStore.*")
    
    print("✓ Qt警告抑制设置成功")


def apply_all_fixes():
    """应用所有Qt修复"""
    print("应用Qt修复...")
    print("=" * 30)
    
    setup_qt_attributes()
    register_qt_types()
    setup_qt_logging()
    suppress_qt_warnings()
    
    print("=" * 30)
    print("✅ Qt修复应用完成")


class QtMessageHandler:
    """自定义Qt消息处理器，过滤不必要的警告"""
    
    def __init__(self):
        self.ignored_patterns = [
            "QVector<int>",
            "QPainter::begin",
            "QPainter::setCompositionMode",
            "QBackingStore::endPaint",
            "Cannot queue arguments",
            "Make sure"
        ]
    
    def message_handler(self, msg_type, context, message):
        """处理Qt消息"""
        # 检查是否应该忽略此消息
        for pattern in self.ignored_patterns:
            if pattern in message:
                return  # 忽略此消息
        
        # 输出其他消息
        if msg_type == 0:  # QtDebugMsg
            print(f"Qt Debug: {message}")
        elif msg_type == 1:  # QtWarningMsg
            print(f"Qt Warning: {message}")
        elif msg_type == 2:  # QtCriticalMsg
            print(f"Qt Critical: {message}")
        elif msg_type == 3:  # QtFatalMsg
            print(f"Qt Fatal: {message}")


def install_message_handler():
    """安装自定义消息处理器"""
    if qInstallMessageHandler is None:
        print("⚠️ qInstallMessageHandler不可用，跳过消息处理器安装")
        return

    try:
        handler = QtMessageHandler()
        qInstallMessageHandler(handler.message_handler)
        print("✓ Qt消息处理器安装成功")
    except Exception as e:
        print(f"⚠️ Qt消息处理器安装失败: {e}")


if __name__ == "__main__":
    # 测试修复功能
    apply_all_fixes()
    install_message_handler()
